<template>
	<view class="material-info">
		<hc-row>
			<hc-col :span="12">
				<view class="info-item info-item-wrap">
					<text class="label">条码号：</text>
					<text class="value value-wrap">{{ item.FBARCODE_NO || "" }}</text>
				</view>
			</hc-col>
		</hc-row>

		<hc-row class="material-row">
			<hc-col :span="6" class="material-col">
				<view class="info-item info-item-wrap">
					<text class="label">接头：</text>
					<text class="value value-wrap">{{ item.FTEXT1 || "" }}</text>
				</view>
			</hc-col>
			<hc-col :span="6" class="material-col">
				<view class="info-item info-item-wrap">
					<text class="label">模号：</text>
					<text class="value value-wrap">{{ item.FTEXT2 || "" }}</text>
				</view>
			</hc-col>
		</hc-row>

		<hc-row>
			<hc-col :span="6">
				<view class="info-item">
					<text class="label">班别：</text>
					<text class="value">{{ item.FTEXT3 || "" }}</text>
				</view>
			</hc-col>
			<hc-col :span="6">
			<view class="info-item">
								<text class="label">盘类：</text>
								<text class="value">{{ item.FTEXT4 || "" }}</text>
							</view>
			</hc-col>
		</hc-row>
		
				<hc-row>
					<hc-col :span="6">
						<view class="info-item">
							<text class="label">扫描状态：</text>
							<text class="value">{{ item.FSCAN_TYPE_NAME || "" }}</text>
						</view>
					</hc-col>
					<hc-col :span="6">
					<view class="info-item">
										<text class="label">扫描时间：</text>
										<text class="value">{{ item.FSCAN_TIME || "" }}</text>
									</view>
					</hc-col>
				</hc-row>
	</view>
</template>

<script>
/**
 * 物料信息显示组件
 * 用于显示物料的基本信息
 */
export default {
	name: "material-info-display",
	
	props: {
		item: {
			type: Object,
			required: true
		},
		editData: {
			type: Object,
			required: true
		},
		showTotal: {
			type: Boolean,
			default: false
		}
	}
}
</script>

<style lang="scss" scoped>
.material-info {
	width: 100%;

	.info-item {
		display: flex;
		align-items: flex-start;
		margin: 8rpx 0;
		font-size: 26rpx;
		width: 100%;
		box-sizing: border-box;

		.label {
			color: #666;
			margin-right: 8rpx;
			flex-shrink: 0; // 标签不缩放
			min-width: 80rpx; // 设置标签最小宽度，保持对齐
			line-height: 1.4;
			align-self: flex-start; // 确保标签向上对齐
		}

		.value {
			color: #333;
			flex: 1;
			line-height: 1.4;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			align-self: flex-start; // 确保值向上对齐
		}

		// 支持换行的样式
		&.info-item-wrap {
			align-items: flex-start; // 强制向上对齐

			.value {
				&.value-wrap {
					white-space: normal;
					word-wrap: break-word;
					word-break: break-all;
					overflow: visible;
					text-overflow: initial;
					margin-top: 0; // 确保没有额外的顶部边距
				}
			}
		}
	}
}

// 确保物料信息行向上对齐
.material-row {
	:deep(.hc-row) {
		display: flex !important;
		align-items: flex-start !important;
		align-content: flex-start !important;
	}
}

.material-col {
	:deep(.hc-col) {
		display: flex !important;
		align-self: flex-start !important;
		vertical-align: top !important;
		align-items: flex-start !important;
	}
}

</style>
