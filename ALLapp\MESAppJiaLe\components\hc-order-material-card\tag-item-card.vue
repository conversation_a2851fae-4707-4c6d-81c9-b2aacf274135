<template>
	<view class="item">
		<!-- 序号标识 -->
		<view class="sequence-number" :style="sequenceNumberStyle">
			<text class="sequence-text">{{ item.sequenceNumber || (index + 1) }}</text>
		</view>

		<view class="item-content">
			<!-- 删除按钮 -->
			<!-- <view v-if="!item.FTAG_GEN_LOG_ID" class="delete-section">
				<hc-button
					type="primary"
					shape="square"
					size="mini"
					class="delete-tag-btn"
					@click="$emit('remove-tag', item, index)"
				>
					删除标签
				</hc-button>
			</view> -->

			<!-- 物料基本信息 -->
			<material-info-display
				:item="item"
				:edit-data="editData"
				:show-total="true"
			/>
			
			<!-- 数量和仓库信息 -->
			<hc-row class="quantity-store-row">
				<hc-col :span="6">
					<view class="quantity-input-section">
						<text class="label">{{ editData.title }}:</text>
						<hc-input
							v-if="!item.FTAG_GEN_LOG_ID"
							type="number"
							v-model="item.FSTK_QTY"
							:customStyle="getQuantityInputStyle(item.FSTK_QTY)"
							class="quantity-input"
							@blur="handleQuantityBlur(item)"
							@input="handleQuantityInput(item)"
							placeholder="请输入收货数量"
						/>
						<text v-else class="quantity-text">{{ item.FSTK_QTY }}</text>
					</view>
				</hc-col>
				<hc-col :span="6">
					<view class="store-section">
						<text class="label">仓库：</text>
						<text
							:class="['store-text', { 'clickable': !item.FTAG_GEN_LOG_ID }]"
							:style="storeTextStyle"
							@click="!item.FTAG_GEN_LOG_ID && $emit('store-click', item, index)"
						>
							{{ formatStoreName(item) }}
						</text>
					</view>
				</hc-col>
			</hc-row>
		</view>
	</view>
</template>

<script>
import MaterialInfoDisplay from './material-info-display.vue'

/**
 * 标签项目卡片组件
 * 用于显示标签登记中的单个标签项目
 */
export default {
	name: "tag-item-card",
	
	components: {
		MaterialInfoDisplay
	},
	
	props: {
		item: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			required: true
		},
		editData: {
			type: Object,
			required: true
		},
		customStyle: {
			type: Object,
			default: () => ({})
		}
	},
	
	computed: {
		storeTextStyle() {
			return {
				color: this.$hc?.requestConfig?.dataColor?.warn || '#007aff',
				'font-weight': 'bold'
			};
		},

		/**
		 * 序号背景色样式 - 使用系统主题色
		 */
		sequenceNumberStyle() {
			const themeColor = this.$themeCurrent?.main || this.$hc?.requestConfig?.dataColor?.warn || '#007aff';
			return {
				backgroundColor: themeColor
			};
		}
	},
	
	methods: {
		/**
		 * 格式化仓库名称显示
		 */
		formatStoreName(item) {
			if (!item.FSTORE_NAME) {
				return '';
			}

			if (item.FSTORE_PLACE_NAME) {
				return `${item.FSTORE_NAME}/${item.FSTORE_PLACE_NAME}`;
			}

			return item.FSTORE_NAME;
		},

		/**
		 * 获取数量输入框样式
		 */
		getQuantityInputStyle(quantity) {
			const baseStyle = { ...this.customStyle };

			// 如果数量为0或空，显示警告颜色
			if (!quantity || parseFloat(quantity) === 0) {
				baseStyle.color = '#ff4757';
				baseStyle.borderColor = '#ff4757';
			}

			return baseStyle;
		},

		/**
		 * 处理数量输入事件
		 */
		handleQuantityInput(item) {
			// 实时验证，但不阻止输入
			this.$emit('quantity-change', item);
		},

		/**
		 * 处理数量失焦事件
		 */
		handleQuantityBlur(item) {
			let quantity = item.FSTK_QTY;

			if (!quantity) {
				// 如果为空，设置为空字符串并提示
				item.FSTK_QTY = '';
				this.$emit('quantity-error', {
					item,
					message: '收货数量不能为空'
				});
				return;
			}

			// 转换为数字
			const numQuantity = parseFloat(quantity);

			// 验证不能为0
			if (numQuantity === 0) {
				this.$emit('quantity-error', {
					item,
					message: '收货数量不能为0'
				});
				return;
			}

			// 去除前导0（保留小数点后的0）
			if (typeof quantity === 'string' && quantity.startsWith('0') && !quantity.startsWith('0.')) {
				const cleanedQuantity = quantity.replace(/^0+/, '') || '0';
				item.FSTK_QTY = cleanedQuantity;
			}

			// 触发数量变更事件
			this.$emit('quantity-change', item);
		}
	}
}
</script>

<style lang="scss" scoped>
.item {
	width: 100%;
	display: flex;
	padding: 15rpx 12rpx;
	border-bottom: 2rpx solid #d1d1d1;
	background-color: #ffffff;
	align-items: flex-start;

	&-content {
		flex: 1;
	}
}

.sequence-number {
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	margin-top: 8rpx;
	flex-shrink: 0;

	.sequence-text {
		color: #ffffff;
		font-size: 20rpx;
		font-weight: bold;
		line-height: 1;
	}
}

.delete-section {
	text-align: right;
	margin-bottom: 10rpx;
	
	.delete-tag-btn {
		margin-left: 20rpx;
	}
}

.quantity-store-row {
	margin-top: 10rpx;
}

.quantity-input-section {
	display: flex;
	align-items: center;
	
	.label {
		font-size: 26rpx;
		color: #333;
		margin-right: 10rpx;
	}
	
	.quantity-input {
		flex: 1;
	}
	
	.quantity-text {
		font-size: 26rpx;
		color: #333;
	}
}

.store-section {
	display: flex;
	align-items: center;
	
	.label {
		font-size: 26rpx;
		color: #333;
		margin-right: 10rpx;
	}
	
	.store-text {
		font-size: 26rpx;
		
		&.clickable {
			cursor: pointer;
		}
	}
}
</style>
