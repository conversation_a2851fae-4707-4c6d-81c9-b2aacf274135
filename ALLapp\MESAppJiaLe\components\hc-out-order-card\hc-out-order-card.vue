<template>
	<hc-layout>
		<!-- 导航栏 -->
		<hc-navbar :title="editData.toptitle" />

		<!-- 搜索表单区域 -->
		<view class="search-form-container">
			<hc-form :model="model" ref="form1" :errorType="errorType">
				<view @touchmove.stop class="form-content">
					<hc-form :model="model" ref="form2" :errorType="errorType">
						<hc-form-item :label="`${editData.searchtitle}:`" label-width="auto" prop="storeQrcode">
							<hc-input type="text" v-model="storeQrcode" input-align="right" placeholder="请扫码或输入编号"
								maxlength="50" class="input-prompt" :focus="storeQrcodeFocus"
								@confirm="storeQrcodeChange" @blur="storeQrcodeChange" />
							<hc-icon icon="icon-scan" @click="storeQrcodeHandle" size="50" />
						</hc-form-item>
					</hc-form>
				</view>

				<!-- 操作按钮组 -->
				<hc-button-group :full="true" class="action-buttons">
					<hc-button type="primary" class="hc-button-full" isGroup isGroupFirst @tap="stocking">
						收&nbsp;&nbsp;货
					</hc-button>
					<hc-button type="primary" class="hc-button-full" isGroup isGroupLast @tap="empty">
						清&nbsp;&nbsp;空
					</hc-button>
				</hc-button-group>
			</hc-form>
		</view>

		<hc-gap :bg-color="borderColor" height="5" />

		<!-- 主要内容区域 -->
		<view class="hc-layout-full">
			<!-- 标签页导航 -->
			<view class="tabs-container">
				<hc-tabs-swiper ref="uTabs" :list="tabList" :current="tabcurrent" @change="tabsChange"
					:active-color="$hc.requestConfig.dataColor.warn" :bar-height="8" :bar-width="barWidth"
					:is-scroll="false" swiperWidth="750" />
			</view>

			<!-- 内容滑动区域 -->
			<swiper :current="tabcurrent" @transition="transition" @animationfinish="animationfinish"
				class="content-swiper">
				<!-- 物料清单页面 -->
				<swiper-item class="swiper-item">
					<scroll-view @touchmove.stop='' scroll-y class="scroll-container"
						style="height: calc(100% - 60rpx);">
						<hc-uni-swipe-action v-if="hasOrderList">
							<hc-uni-swipe-action-item v-for="(item, index) in orderList" :key="`order-${index}`"
								:extra="item" :index="index" :right-options="swipe.matoptions">
								<material-item-card :item="item" :index="index" :edit-data="editData"
									:show-tag-button="true" @click="ItemClick" @add-tag="AddTagInfo" />
							</hc-uni-swipe-action-item>
						</hc-uni-swipe-action>
					</scroll-view>
				</swiper-item>
				<!-- 标签登记页面 -->
				<swiper-item class="swiper-item">
					<scroll-view @touchmove.stop='' scroll-y class="scroll-container"
						style="height: calc(100% - 60rpx);">
						<hc-uni-swipe-action v-if="hasOrderTagList">
							<hc-uni-swipe-action-item v-for="(item, index) in orderTagList" :key="`tag-${index}`"
								:extra="item" :index="index" :right-options="swipe.options">
								<tag-item-card :item="item" :index="index" :edit-data="editData"
									:custom-style="customStyle"
									@remove-tag="RemoveTagInfo"
									@store-click="stkclick"
									@quantity-change="handleQuantityChange"
									@quantity-error="handleQuantityError" />
							</hc-uni-swipe-action-item>
						</hc-uni-swipe-action>
					</scroll-view>
				</swiper-item>

			</swiper>
		</view>

		<!-- 仓库选择器 -->
		<hc-select v-model="Selshow" mode="mutil-column-auto" :list="Stklist" @confirm="Selconfirm" />

		<!-- 底部固定说明 -->
		<view class="fixed-bottom-tip">
			<text class="tip-text">左滑可以删除标签细项</text>
		</view>
	</hc-layout>


</template>

<script>
	import MaterialInfoDisplay from './material-info-display.vue'
	import MaterialItemCard from './material-item-card.vue'
	import TagItemCard from './tag-item-card.vue'

	/**
	 * 出库订单卡片组件
	 * 用于委外出库操作，支持物料清单管理和标签登记
	 */
	export default {
		name: "hc-out-order-card",
		components: {
			MaterialInfoDisplay,
			MaterialItemCard,
			TagItemCard
		},

		props: {
			index: {
				type: Number,
				default: 0
			},
			editData: {
				type: Object,
				default () {
					return {
						title: "出库数",
						field: "FNUM",
						toptitle: "委外出库",
						billtype: "OutOrderOutScan",
						searchtitle: "委外单号"
					}
				}
			}
		},

		data() {
			return {
				// 标签页配置
				tabList: [{
						name: "物料清单"
					},
					{
						name: "标签登记"
					}
				],
				tabcurrent: 0,
				barWidth: 250,

				// API配置
				api: {
					apiGetItemList: "/api/MES017Tag/GeneratePredictBomCodeList",
					apiGetStore: "/api/STK001Store/GeStoreAndPlaceAsync",
					apiStocking: "/api/MES017Tag/GenrateOrderInsertAsync"
				},

				// 样式配置
				customStyle: {
					color: this.$hc.requestConfig.dataColor.warn,
					"font-weight": "bold"
				},
				borderColor: this.$hc.color.borderColor,
				errorType: ['message'],

				// 界面状态
				Selshow: false,
				currentItemIndex: null,

				// 数据存储
				Stklist: [],
				orderList: [],
				orderTagList: [],
				modelCopy: "",
				tagSequenceCounter: 0, // 标签序号计数器

				// 滑动操作配置
				swipe: {
					matoptions: [],
					options: [{
						text: '删除',
						style: {
							backgroundColor: '#007aff',
							margin: '10rpx 0rpx 10rpx -8rpx',
							borderRadius: '40rpx',
						},
						"app-plus": {
							"bounce": "none"
						}
					}],
				},

				// 表单数据
				storeQrcode: '',
				storeQrcodeFocus: true,
				model: {
					FSTORE_ID_NAME: '',
					FSTORE_ID: "",
					FSTORE_PLACE_NAME: '',
					FSTORE_PLACE_CODE: "",
					FSTORE_PLACE_ID: "",
				}
			}
		},

		computed: {
			// 是否有订单列表数据
			hasOrderList() {
				return this.orderList && this.orderList.length > 0;
			},

			// 是否有标签列表数据
			hasOrderTagList() {
				return this.orderTagList && this.orderTagList.length > 0;
			},

			// 仓库文本样式
			storeTextStyle() {
				return {
					color: this.$hc.requestConfig.dataColor.warn,
					'font-weight': 'bold'
				};
			}
		},

		mounted() {
			this.initializeComponent();
		},

		methods: {
			/**
			 * 初始化组件
			 */
			initializeComponent() {
				this.modelCopy = JSON.parse(JSON.stringify(this.model));
				this.swipe.options[0].style.backgroundColor = this.$themeCurrent.main;
				this.calculateBarWidth();
				this.getStoreData();
			},

			/**
			 * 计算标签页宽度
			 */
			calculateBarWidth() {
				const tabCount = this.tabList.length;
				this.barWidth = 750 / tabCount;
			},

			/**
			 * 添加标签信息
			 */
			AddTagInfo(item) {
				if (!item.TagNum) {
					item.TagNum = 0;
				}
				item.TagNum += 1;

				// 生成新的序号
				this.tagSequenceCounter += 1;

				this.orderTagList.push({
					...item,
					sequenceNumber: this.tagSequenceCounter, // 添加序号
					FSTK_QTY: '' // 初始化收货数量为空
				});
			},

			/**
			 * 删除标签信息
			 */
			RemoveTagInfo(item, index) {
				this.orderTagList.splice(index, 1);
				const sourceItem = this.orderList.find(x =>
					x.FBILL_SOURCE_ITEM_ID === item.FBILL_SOURCE_ITEM_ID
				);
				if (sourceItem && sourceItem.TagNum > 0) {
					sourceItem.TagNum--;
				}
			},

			/**
			 * 条目点击事件
			 */
			ItemClick(item, index) {
				// 可以在这里添加具体的点击逻辑
				console.log('Item clicked:', item, index);
			},

			/**
			 * 处理数量变更事件
			 */
			handleQuantityChange(item) {
				// 可以在这里添加数量变更后的逻辑
				console.log('Quantity changed:', item.FSTK_QTY, 'for item:', item.FMATERIAL_NAME);
			},

			/**
			 * 处理数量错误事件
			 */
			handleQuantityError(errorInfo) {
				const { item, message } = errorInfo;
				this.$hc.Command.NotifyDialog({
					content: `序号${item.sequenceNumber}: ${message}`,
					confirm() {}
				});
			},

			/**
			 * Swiper过渡动画
			 */
			transition(e) {
				const dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},

			/**
			 * Swiper动画结束
			 */
			animationfinish(e) {
				const current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.tabcurrent = current;
			},

			/**
			 * 仓库点击事件
			 */
			stkclick(item, index) {
				this.currentItemIndex = index;
				this.Selshow = true;
			},

			/**
			 * 标签页切换
			 */
			tabsChange(val) {
				this.tabcurrent = val;
			},

			/**
			 * 仓库选择确认
			 */
			Selconfirm(val) {
				if (!val || val.length === 0) return;

				const [storeInfo, placeInfo] = val;
				const item = this.orderTagList[this.currentItemIndex];

				if (item) {
					Object.assign(item, {
						FSTORE_ID: storeInfo?.value || '',
						FSTORE_NAME: storeInfo?.label || '',
						FSTORE_PLACE_ID: placeInfo?.value || '',
						FSTORE_PLACE_NAME: placeInfo?.label || ''
					});
				}
			},

			/**
			 * 格式化仓库名称显示
			 */
			formatStoreName(item) {
				if (this.$hc.Command.IsEmpty(item.FSTORE_NAME)) {
					return '';
				}

				if (!this.$hc.Command.IsEmpty(item.FSTORE_PLACE_NAME)) {
					return `${item.FSTORE_NAME}/${item.FSTORE_PLACE_NAME}`;
				}

				return item.FSTORE_NAME;
			},

			/**
			 * 验证标签数量是否合法
			 * 检查同一个 FBILL_SOURCE_ITEM_ID 的标签数量不能大于 总数-已收货数
			 * 同时检查收货数量不能为0或空
			 */
			validateTagQuantities() {
				const tagCountMap = new Map();

				// 首先检查每个标签的收货数量
				for (let i = 0; i < this.orderTagList.length; i++) {
					const tagItem = this.orderTagList[i];
					const quantity = tagItem.FSTK_QTY;

					// 检查数量是否为空
					if (!quantity || quantity === '') {
						return {
							isValid: false,
							message: `序号${tagItem.sequenceNumber}: 收货数量不能为空`
						};
					}

					// 检查数量是否为0
					const numQuantity = parseFloat(quantity);
					if (isNaN(numQuantity) || numQuantity <= 0) {
						return {
							isValid: false,
							message: `序号${tagItem.sequenceNumber}: 收货数量必须大于0`
						};
					}

					// 统计每个 FBILL_SOURCE_ITEM_ID 的标签数量
					const itemId = tagItem.FBILL_SOURCE_ITEM_ID;
					if (tagCountMap.has(itemId)) {
						tagCountMap.set(itemId, tagCountMap.get(itemId) + numQuantity);
					} else {
						tagCountMap.set(itemId, numQuantity);
					}
				}

				// 验证每个物料项目的数量限制
				for (const [itemId, tagQuantity] of tagCountMap) {
					const orderItem = this.orderList.find(item => item.FBILL_SOURCE_ITEM_ID === itemId);

					if (orderItem) {
						const totalQuantity = parseFloat(orderItem.FNUM) || 0;
						const scannedQuantity = parseFloat(orderItem.FSCAN_NUM) || 0;
						const availableQuantity = totalQuantity - scannedQuantity;

						if (tagQuantity > availableQuantity) {
							return {
								isValid: false,
								message: `物料"${orderItem.FMATERIAL_NAME || orderItem.FMATERIAL_CODE}"的标签数量(${tagQuantity})超过了可收货数量(${availableQuantity})`
							};
						}
					}
				}

				return {
					isValid: true,
					message: ''
				};
			},
			//根据单号获取数据
			getOrderList() {
				const _this = this;
				const hc = this.$hc;
				if (_this.$hc.Command.IsEmpty(_this.storeQrcode)) {
					return
				}
				const OrderInf = {
					"FBILL_SOURCE_NO": _this.storeQrcode,
					"FBILL_TYPE": _this.editData.billtype
				};
				hc.Command.LoadData(_this.api.apiGetItemList, {
					model: OrderInf
				}, (res) => {
					if (res.StatusCode == 200) {


						_this.orderList = res.Entity.TagBillList

						_this.orderTagList = res.Entity.TagScanLog



						// _this.orderList.forEach(item => {
						// 	item.TagNum = 0 // 或者你需要的初始值，比如 null、""、item.SomeField 等
						// })
					} else {
						hc.Command.ShowResMessage(res);
					}
				})

			},
			//根据货位号获取货位信息方法
			getStoreData() {
				let _this = this;
				// console.log('22222')
				_this.$hc.Command.LoadData(_this.api.apiGetStore, {}, (res) => {
					if (res.StatusCode === 200) {
						if (res.Entity.length > 0) {
							const cascaderOptions = res.Entity.map(store => {
								const node = {
									value: store.FSTORE_ID,
									label: store.FSTORE_NAME

								};

								if (store.FIF_ENABLE_PLACE && store.PLACE_LIST.length > 0) {
									node.children = store.PLACE_LIST.map(place => ({
										value: place.FSTORE_PLACE_ID,
										label: place.FSTORE_PLACE_NAME
									}));
								}

								return node;
							});
							cascaderOptions.sort((a, b) => {
								const aHasChildren = a.children && a.children.length > 0;
								const bHasChildren = b.children && b.children.length > 0;
								return aHasChildren === bHasChildren ? 0 : aHasChildren ? -1 : 1;
							});
							console.log(cascaderOptions);
							_this.Stklist = cascaderOptions
						}

					} else {
						_this.storeQrcode = ''
						_this.$hc.Command.ShowResMessage(res);
					}
				})

			},
			//失焦，输入时方法 change
			storeQrcodeChange() {
				this.getOrderList()
			},
			//搜索图标方法
			storeQrcodeHandle() {
				let _this = this;
				// 扫码图标点击
				uni.scanCode({
					success(res) {
						_this.storeQrcode = res.result;
						_this.getStoreData();
					},
					fail(res) {},
					complete(res) {},
				});
			},


			//扫描后根据单据编号、物料编码，搜索方法
			getMaterialData() {
				let _this = this;

				if (!_this.materialQrcode) {
					return
				}

				let model = {
					"PageSize": 30,
					"PageIndex": 1,
					"WhereGroup": {
						"Groups": [{
							"Items": [{
								"FieldName": "a.FMATERIAL_CODE",
								"FieldDataType": "",
								"OperatorType": "Equal",
								"Value": _this.materialQrcode
							}, ],
							"GroupType": "OR"
						}],
						"Items": [],
						"GroupType": "AND"
					}
				}
				_this.$hc.Command.LoadData(_this.api.apiGetOrderMaterial, {
					model
				}, (res) => {
					if (res.StatusCode == 200) {
						if (res.Entity.length > 0) { //每次添加不只有一个物料
							_this.formatModels(res.Entity) //格式化models方法
							// _this.formatTagModels(res.)
						} else {
							return _this.$hc.Command.NotifyDialog({
								content: "未找到对应物料",
								confirm() {}
							});
						}
					} else {
						_this.$hc.Command.ShowResMessage(res);
					}
				})
			},
			//扫描图标方法
			materialQrCodeHandle: function() {
				let _this = this;
				// 扫码图标点击
				uni.scanCode({
					success(res) {
						_this.materialQrcode = res.result;
						_this.getMaterialData();
					},
					fail(res) {

					},
					complete(res) {

					},
				});
			},
			//物料二维码 change
			materialQrcodeChange() {
				this.getMaterialData()
			},

			//格式化Models方法
			formatModels(data) {
				let models = this.models.slice()
				for (let i of data) {
					let ifget = false;
					for (let j of models) {
						if (i.FMATERIAL_ID == j.FMATERIAL_ID) {
							ifget = true;
							continue;
						}
					}
					if (!ifget) {
						models.unshift(i) //在数组添加第一位数组
						i.FSTK_UNIT_QTY = '' //将入库数默认设置为0
					}
				}
				this.models = models

			},
			//格式化Models方法
			formatTagModels(data) {
				let models = this.Tagmodels.slice()
				for (let i of data) {
					let ifget = false;
					for (let j of models) {
						if (i.FMATERIAL_ID == j.FMATERIAL_ID) {
							ifget = true;
							continue;
						}
					}
					if (!ifget) {
						models.unshift(i) //在数组添加第一位数组
						i.FSTK_UNIT_QTY = '' //将入库数默认设置为0
					}
				}
				this.Tagmodels = models

			},

			//入库方法
			stocking() {
				const _this = this
				const hc = this.$hc;

				// 验证数量：同一个 FBILL_SOURCE_ITEM_ID 的标签数量不能大于 总数-已收货数
				const validationResult = this.validateTagQuantities();
				if (!validationResult.isValid) {
					hc.Command.NotifyDialog({
						content: validationResult.message,
						confirm() {}
					});
					return;
				}

				const models = this.orderTagList;

				console.log(JSON.stringify(models))
				// hc.Command.LoadData(_this.api.apiStocking, {
				// 	models
				// }, (res) => {
				// 	if (res.StatusCode == 200) {
				// 		// _this.$hc.Command.ToSaveSuccessPage({
				// 		// 	buttons: [{
				// 		// 		pageUrl: "OtherStockingApp",
				// 		// 		text: "继续入库"
				// 		// 	}, ]
				// 		// })
				// 		hc.Command.NotifyDialog({
				// 			content: "入库完成",
				// 			confirm() {
				// 				_this.emptyMaterials() //完成之后清空物料信息
				// 				// uni.navigateBack();
				// 			}
				// 		});
				// 	} else {
				// 		hc.Command.ShowResMessage(res);

				// 		// _this.$hc.Command.NotifyDialog({
				// 		// 	content: "未找到对应物料",
				// 		// 	confirm() {}
				// 		// });
				// 	}
				// }, {
				// 	disableLoading: false
				// })
			},
			onRefresh() {
				// this.$refs.hcScroll.mescroll.endBySize(rowData.length, res.Pager.TotalRecords);

				this.$refs.hcScroll.mescroll.endBySize(10, this.models.length);
			},
			reachBottom() {
				this.$refs.hcScroll.mescroll.endBySize(10, this.models.length);
			},
			onTagRefresh() {
				// this.$refs.hcScroll.mescroll.endBySize(rowData.length, res.Pager.TotalRecords);

				this.$refs.hcScroll.mescroll.endBySize(10, this.Tagmodels.length);
			},
			reachTagBottom() {
				this.$refs.hcScroll.mescroll.endBySize(10, this.Tagmodels.length);
			},


			//写入警告色
			setWarnColor() {
				let color = `${this.$hc.requestConfig.dataColor.warn}`
				this.inputUserStyle["color"] = color
			},
			//设定员工信息
			setEmpInfo: function() {
				let userInfo = this.$hc.Command.GetUserInfo();
				if (userInfo) {
					this.model.FEMP_NAME = userInfo.UserPsnName;
					this.model.FEMP_ID = userInfo.UserPsnId;
				}
			},
			toNavBack() {
				this.$hc.Command.SwitchTab("IndexPage");
			},
			//清空方法
			empty() {
				this.emptyMaterials()
				this.emptyStore()
			},
			//清空物料方法
			emptyMaterials() {
				this.orderList = []
				this.orderTagList = []
				this.storeQrcode = ''
				this.tagSequenceCounter = 0 // 重置序号计数器
			},
			//清空仓库信息方法
			emptyStore() {
				this.model = this.modelCopy
				this.storeQrcode = ''
				this.havePlace = true
			},
			//左滑删除按钮方法
			onClick(index) {
				let models = this.models
				models.splice(index, 1)
			},
			MathFixedFun(value) {
				return this.$hc.Command.MathFixedFun(value);
			}


		}
	}
</script>

<style lang="scss" scoped>
	/**
 * 出库订单卡片组件样式
 */

	/* 搜索表单区域 */
	.search-form-container {
		background-color: #ffffff;

		.form-content {
			height: 100%;
		}

		.action-buttons {
			padding: 7rpx 10rpx;
		}
	}

	/* 标签页容器 */
	.tabs-container {
		background-color: #ffffff;
		border-bottom: 2rpx solid #f07b3e;
	}

	/* 内容滑动区域 */
	.content-swiper {
		height: 100%;
	}

	.swiper-item {
		height: 100%;
	}

	.scroll-container {
		height: 100%;
	}

	/* 输入框样式 */
	.input-prompt {
		font-weight: 900;
	}

	/* 物料项目卡片 */
	.item {
		width: 100%;
		display: flex;
		padding: 15rpx 12rpx;
		border-bottom: 2rpx solid #d1d1d1;
		background-color: #ffffff;

		&-content {
			flex: 1;

			&-title {
				font-size: 28rpx;
				line-height: 50rpx;
			}

			&-type {
				margin: 14rpx 0;
				font-size: 26rpx;
				color: $u-tips-color;
			}
		}
	}

	/* 标签相关样式 */
	.tag-section {
		display: flex;
		align-items: center;
		margin-top: 10rpx;

		.tag-count {
			font-size: 26rpx;
			color: #666;
		}

		.add-tag-btn {
			margin-left: 20rpx;
		}
	}

	.delete-section {
		text-align: right;
		margin-bottom: 10rpx;

		.delete-tag-btn {
			margin-left: 20rpx;
		}
	}

	/* 数量和仓库信息行 */
	.quantity-store-row {
		margin-top: 10rpx;
	}

	.quantity-input-section {
		display: flex;
		align-items: center;

		.label {
			font-size: 26rpx;
			color: #333;
			margin-right: 10rpx;
		}

		.quantity-input {
			flex: 1;
		}

		.quantity-text {
			font-size: 26rpx;
			color: #333;
		}
	}

	.store-section {
		display: flex;
		align-items: center;

		.label {
			font-size: 26rpx;
			color: #333;
			margin-right: 10rpx;
		}

		.store-text {
			font-size: 26rpx;

			&.clickable {
				cursor: pointer;
			}
		}
	}

	/* 文本省略样式 */
	.pro-left {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.pro-right {
		width: 250rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 底部固定说明 */
	.fixed-bottom-tip {
		// position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.7);
		padding: 10rpx;
		text-align: center;
		// z-index: 999;

		.tip-text {
			color: #ffffff;
			font-size: 24rpx;
			line-height: 1.2;
		}
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.item {
			padding: 10rpx 8rpx;
		}

		.tag-section,
		.quantity-store-row {
			flex-direction: column;
			align-items: flex-start;

			.add-tag-btn,
			.delete-tag-btn {
				margin-left: 0;
				margin-top: 10rpx;
			}
		}
	}
</style>